<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>06_JS_Conditionals IF</title>
  </head>
  <body>
    <script>
      var age = prompt("How old are you?");
      age = parseInt(age); // ili odma parsIntat prompt - parseInt(prompt("How old are you?"));
      if (age >= 18) {
        document.write("You are of legal age.");
      } else if (age < 18) {
        document.write("You are not of legal age.");
      } else {
        document.write("You have not entered a number.");
      }

      // Provjeriti je li user ima više od 18 godina, je li ima aktivan account
      // i je li ima aktivnu subcription.

      let userAge = 17;
      let hasSubscription = true;
      let isAccountActive = true;

      if (userAge > 18 && hasSubscription && isAccountActive) {
        console.log("Access granted to user!");
      } else if (userAge > 18 && !hasSubscription) {
        console.log("User is valid of age, but has no subscription.");
      } else if (!isAccountActive) {
        console.log("User account is not active.");
      } else {
        console.log("Access denied!");
      }

      // Primjer korištenja OR operatora

      let isAdmin = false;
      let isModerator = true;

      if(isAdmin || isModerator){
        console.log("User have administrative privilages.");
      } else {
        console.log("User have no administrative privilages.");
      }

     // Tražiti korisnika unos broja i provjeriti je li paran ili neparan.

     let number = prompt("Unesite broj: ");
     number = parseInt(number);

     if(number % 2 === 0){  // radimo preko modulo operatora - ako je ostatak pri dijeljenju sa 2 jednak 0, onda je paran broj.
        console.log("Paran.");  // ako negiramo uvjet s != 0, onda je provjera obrnuto.
     } else {
        console.log("Neparan.");
     }

    </script>
  </body>
</html>
