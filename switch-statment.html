<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>06_JS_Conditionals Switch Statment</title>
  </head>
  <body>
    <script>
      var grade = 2;

      switch (grade) {
        case 5:
          document.write("Odličan!");
          break;
        case 4:
          document.write("Vrlo dobar!");
          break;
        case 3:
          document.write("Dobar!");
          break;
        case 2:
          document.write("Dovoljan!");
          break;
        case 1:
          document.write("Nedovoljan!");
          break;
        default:
          document.write("Nije upisali valjanu ocjenu!");
          break;
      }

      // Primjer korištenja Switcha s višestrukim slu<PERSON> ( multiple cases )

      let userStatus = pending;

      switch (userStatus) {
        case "active":
        case "confirmed":
          console.log("Korisnikov račun je aktivan!");
          break;
        case "pending":
        case "on hold":
          console.log(
            "Korisnikov račun je na čekanju ili u tijeku verifikacije!"
          );
          break;
        case "suspended":
          console.log("Korisnikov račun je suspendiran!");
          break;
        default:
          console.log("Nije poznat status korisnika!");
          break;
      }

      let userRole = prompt(
        "Which is your role? (admin,editor,subscriber,moderator)"
      );

      switch (userRole) {
        case "admin":
          alert("You have access to the admin panel!");
          break;
        case "editor":
          alert("You have access to the editor panel!");
          break;
        case "moderator":
          alert("You have access to the moderator panel!");
          break;
        case "subscriber":
          alert("You have access to the subscriber panel!");
          break;
        default:
          alert("You don't have access to any panel!");
          break;
      }
    </script>
  </body>
</html>
