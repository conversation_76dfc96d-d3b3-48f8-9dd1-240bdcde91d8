<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>06_JS_Conditionals - Ternary Operator</title>
  </head>
  <body>
    <script>
  
// Ternary Operator - je skraceni nacin za pisanje IF ELSE naredbe

let age = 20;
let isAdult = (age >= 18) ? true : false;

//   if (age >= 18){
 //   isAdult = true;
//   } else {
//    isAdult = false;
//   }

console.log(isAdult);


// Nested ternary operator

let score = 85;
let grade = (score >=90)? "A" : (score >= 80) ? "B" : (Score >= 70) ? "C" : (score >= 60) ? "D" : "F";
console.log(score, grade);


    </script>
  </body>
</html>
